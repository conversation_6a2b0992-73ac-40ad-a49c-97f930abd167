from playwright.async_api import async_playwright
import asyncio
import json
import csv
import re # Import regex for potential price cleaning

async def scrape_page():
    browser = None  # Initialize browser to None for finally block
    try:
        async with async_playwright() as p:
            # Use headless=False initially for debugging, set to True for production
            browser = await p.chromium.launch(headless=False)
            context = await browser.new_context()
            page = await context.new_page()

            url = "https://cremecastle.in/collections/bachelor-bachelorette-cakes"
            print(f"Navigating to page: {url}")
            # Use wait_until="domcontentloaded" for faster initial load
            await page.goto(url, wait_until="domcontentloaded")

            # Wait for a reasonable time for potential popups or dynamic content
            print("Waiting for initial content and potential popups...")
            # Increased wait time to allow page elements and potential popups to load
            await page.wait_for_timeout(7000)

            # --- Robust Popup Handling ---
            popup_selector = "div#popup"
            popup = await page.query_selector(popup_selector)
            popup_closed_successfully = False

            if popup:
                print("Popup (div#popup) element found.")
                # Check if the popup is actually visible before trying to close
                is_popup_visible = await popup.evaluate('''
                    (element) => {
                        const computedStyles = window.getComputedStyle(element);
                        const rect = element.getBoundingClientRect();
                        return computedStyles.display !== "none" &&
                               computedStyles.visibility !== "hidden" &&
                               computedStyles.opacity !== "0" &&
                               rect.width > 0 && rect.height > 0;
                    }
                ''')

                if is_popup_visible:
                    print("Popup is visible. Trying common dismissal methods.")
                    try:
                        # Attempt to press Escape key (common way to close modals)
                        print("Attempting to press Escape key...")
                        # Ensure the body or a main container is focused to receive key events
                        await page.locator('body').press('Escape')
                        await page.wait_for_timeout(2000) # Wait for the popup to disappear

                        # Re-check if the popup is still visible
                        is_popup_visible_after_escape = await page.evaluate(f'''
                            (selector) => {{
                                const element = document.querySelector(selector);
                                if (!element) return false; // Element no longer in DOM
                                const computedStyles = window.getComputedStyle(element);
                                const rect = element.getBoundingClientRect();
                                return computedStyles.display !== "none" &&
                                       computedStyles.visibility !== "hidden" &&
                                       computedStyles.opacity !== "0" &&
                                       rect.width > 0 && rect.height > 0;
                            }}
                        ''', popup_selector)

                        if not is_popup_visible_after_escape:
                            print("Popup dismissed by Escape key.")
                            popup_closed_successfully = True
                        else:
                            print("Popup still visible after pressing Escape. Trying to click outside.")
                            # Attempt to click outside the popup area (assuming it has a backdrop)
                            # You might need to adjust coordinates based on page structure.
                            # Clicking near the top-left corner is often safe.
                            try:
                                print("Attempting to click outside popup...")
                                await page.mouse.click(10, 10)
                                await page.wait_for_timeout(2000)

                                is_popup_visible_after_click = await page.evaluate(f'''
                                    (selector) => {{
                                        const element = document.querySelector(selector);
                                        if (!element) return false; // Element no longer in DOM
                                        const computedStyles = window.getComputedStyle(element);
                                        const rect = element.getBoundingClientRect();
                                        return computedStyles.display !== "none" &&
                                               computedStyles.visibility !== "hidden" &&
                                               computedStyles.opacity !== "0" &&
                                               rect.width > 0 && rect.height > 0;
                                    }}
                                ''', popup_selector)

                                if not is_popup_visible_after_click:
                                    print("Popup dismissed by clicking outside.")
                                    popup_closed_successfully = True
                                else:
                                    print("Popup still visible after clicking outside.")

                            except Exception as e:
                                print(f"Error attempting to click outside popup: {e}")

                    except Exception as e:
                        print(f"Error during popup dismissal attempts: {e}")

                    if not popup_closed_successfully:
                        print("Warning: Could not confirm popup dismissal using common methods.")
                else:
                    print("Popup (div#popup) found but was not visible.")
            else:
                print("Popup (div#popup) element not found on the page.")
            # --- End Popup Handling ---

            # Wait for the product grid to load after potential popup dismissal
            # Use state='attached' to wait for the element to be in the DOM,
            # then add a short timeout to ensure rendering.
            print("Waiting for product cards to be attached to DOM...")
            try:
                # Use wait_for_function to handle dynamic loading
                await page.wait_for_function(
                    "document.querySelectorAll('.product-card').length > 0",
                    timeout=90000  # Increased timeout to 90 seconds
                )
                print("Product cards found in DOM.")
            except Exception as e:
                print("Error waiting for product cards:", e)
                # Capture a screenshot for debugging
                await page.screenshot(path="error_screenshot.png")
                raise e


            # Collect summary stats
            print("Collecting summary stats...")
            total_results = 0
            total_pages = 0

            # Example of robust text extraction and parsing
            results_summary = await page.query_selector(".results-summary")
            if results_summary:
                try:
                    total_results_text = await results_summary.text_content()
                    print(f"Results summary text: '{total_results_text}'")
                    # Use regex to find numbers in the string, assuming format like "X results" or "Showing X of Y results"
                    match = re.search(r'(\d+)', (total_results_text or ""))
                    if match:
                        total_results = int(match.group(1))
                        print(f"Parsed total results: {total_results}")
                    else:
                        print(f"Could not parse total results number from: {total_results_text}")
                except Exception as e:
                    print(f"Error parsing total results summary: {e}")
            else:
                print("Warning: .results-summary not found.")

            # Find the last page number from pagination if available
            # This selector might need adjustment based on the actual pagination structure.
            # Look for the last element with the class "page-number" within the pagination block.
            paginator_last_button = await page.query_selector(".pagination .page-number:last-child")
            if paginator_last_button:
                try:
                    total_pages_text = await paginator_last_button.text_content()
                    print(f"Last pagination button text: '{total_pages_text}'")
                    if total_pages_text and total_pages_text.strip().isdigit():
                        total_pages = int(total_pages_text.strip())
                        print(f"Parsed total pages: {total_pages}")
                    else:
                        # Fallback if last button is not just a number (e.g., contains arrows)
                        page_numbers = await page.query_selector_all(".pagination .page-number")
                        # Filter out potentially non-numeric links if needed, or assume count is pages
                        total_pages = len(page_numbers) # This might be an overestimate if non-number links are included
                        print(f"Counted pagination elements: {total_pages} (might include non-page links)")

                except Exception as e:
                     print(f"Error parsing total pages from pagination: {e}")
            else:
                 print("Warning: .pagination .page-number not found. Cannot reliably determine total pages this way.")
                 # If pagination structure is complex or missing, you might need a different strategy,
                 # like scrolling and loading more, or inferring from total results.
                 # For now, if total_pages is 0, the script will only scrape the first page.


            print(f"Estimated total pages to scrape: {total_pages if total_pages > 0 else 1}")


            # Build complete product-link list - iterate through pages if total_pages is known
            product_links = set()
            # Iterate up to total_pages or just 1 if total_pages is not determined
            pages_to_scrape = range(1, total_pages + 1) if total_pages > 0 else range(1, 2)

            for page_num in pages_to_scrape:
                 current_page_url = f"{url.split('?')[0]}?q=Bachelorette&page={page_num}" # Construct URL correctly
                 print(f"Going to page {page_num}: {current_page_url}")
                 # Use wait_until="domcontentloaded" for faster navigation between pages
                 await page.goto(current_page_url, wait_until="domcontentloaded")
                 # Wait for product cards to load on this specific page
                 await page.wait_for_selector(".product-card", state='attached', timeout=30000)
                 await page.wait_for_timeout(1000) # Short wait after cards are attached

                 product_elements = await page.query_selector_all(".product-card")
                 print(f"Found {len(product_elements)} product cards on page {page_num}.")
                 for product in product_elements:
                     try:
                         product_link_el = await product.query_selector("a")
                         if product_link_el:
                             product_url_relative = await product_link_el.get_attribute("href")
                             if product_url_relative:
                                 # Construct full URL robustly
                                 product_url = page.url.split('/pages/')[0] + product_url_relative
                                 product_links.add(product_url)
                             else:
                                 print("Warning: Product link href attribute is empty.")
                         else:
                             print("Warning: Product link (a tag) not found within a product card.")
                     except Exception as e:
                         print(f"Error extracting product URL from a card: {e}")

            print(f"Total unique product links collected: {len(product_links)}")

            # Extract per-product details
            product_details = []
            print(f"Extracting details for {len(product_links)} products...")
            for i, product_url in enumerate(list(product_links)): # Convert set to list to iterate with index
                print(f"Scraping product {i+1}/{len(product_links)}: {product_url}")
                product_page = None # Initialize to None for finally block
                try:
                    # Use a new page for each product to avoid state issues
                    product_page = await context.new_page()
                    await product_page.goto(product_url, wait_until="domcontentloaded")

                    # --- CORRECTED WAIT: Wait for a product-specific element ---
                    # Example: wait for the product title to appear
                    await product_page.wait_for_selector("h1.product-title", state='attached', timeout=30000)
                    await product_page.wait_for_timeout(3000) # Wait a bit more for content rendering

                    # Extract details - using text_content() and inner_html()
                    product_name_el = await product_page.query_selector("h1.product-title")
                    product_name = await product_name_el.text_content() if product_name_el else ""
                    print(f" - Name: {(product_name or '').strip()}")

                    price_regular_el = await product_page.query_selector(".price-regular")
                    price_regular = await price_regular_el.text_content() if price_regular_el else ""
                    print(f" - Regular Price: {(price_regular or '').strip()}")


                    price_sale_el = await product_page.query_selector(".price-sale")
                    price_sale = await price_sale_el.text_content() if price_sale_el else ""
                    print(f" - Sale Price: {(price_sale or '').strip()}")

                    # Verify these selectors on a product page if needed
                    sku_el = await product_page.query_selector(".sku") # Check actual class name
                    sku = await sku_el.text_content() if sku_el else ""

                    availability_el = await product_page.query_selector(".availability") # Check actual class name
                    availability = await availability_el.text_content() if availability_el else ""

                    description_el = await product_page.query_selector(".description") # Check actual class name
                    description_html = await description_el.inner_html() if description_el else ""

                    # Add scraping logic for other fields (weight_or_size, rating, reviews, images, variants, collections)
                    # based on actual selectors on the product page. Example:
                    # weight_or_size = await product_page.locator(".weight-size").text_content() if await product_page.locator(".weight-size").count() > 0 else ""
                    # image_urls = await product_page.evaluate('''() => {
                    #     const images = Array.from(document.querySelectorAll('.product-images img')); // Adjust selector
                    #     return images.map(img => img.src);
                    # }''')


                    product_details.append({
                        "product_name": (product_name or "").strip(),
                        "product_url": product_url,
                        "price_regular": (price_regular or "").strip(),
                        "price_sale": (price_sale or "").strip(),
                        "variant_options": [], # Populate this list if you scrape variants
                        "sku": (sku or "").strip(),
                        "availability": (availability or "").strip(),
                        "description_html": description_html.strip(),
                        "image_urls": [], # Populate this list if you scrape images
                        "weight_or_size": "", # Populate this string if you scrape
                        "collections": [], # Populate this list if you scrape collections
                        "rating": "", # Populate this string if you scrape
                        "review_count": "" # Populate this string if you scrape
                    })

                except Exception as e:
                    print(f"Error extracting details for {product_url}: {e}")
                finally:
                     # Ensure the page is closed even if errors occur during scraping
                    if product_page and not product_page.is_closed():
                        await product_page.close()


            # Save results
            summary = {
                "total_results_from_summary": total_results,
                "estimated_total_pages_from_pagination": total_pages,
                "unique_links_collected": len(product_links),
                "details_extracted_for": len(product_details)
            }

            print("\nScraping Summary:")
            print(json.dumps(summary, indent=4))

            # Save to CSV
            print("\nSaving product details to CSV...")
            with open("product_details.csv", "w", encoding="utf-8", newline="") as csv_file:
                fieldnames = [
                    "product_name", "product_url", "price_regular", "price_sale",
                    "variant_options", "sku", "availability", "description_html",
                    "image_urls", "weight_or_size", "collections", "rating", "review_count"
                ]
                writer = csv.DictWriter(csv_file, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(product_details)

            print("--End of Data--")

    except Exception as error:
        print(f"An error occurred during the scraping process: {error}")
    finally:
        # Ensure the browser is closed even if errors occur
        if browser:
            await browser.close()
            print("Browser closed.")

# Execute the scraping function
if __name__ == "__main__":
    asyncio.run(scrape_page())