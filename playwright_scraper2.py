from playwright.async_api import async_playwright
import asyncio
import json
import csv

async def scrape_page():
    try:
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False)
            context = await browser.new_context()
            page = await context.new_page()

            url = "https://cremecastle.in/pages/searchtap-search?q=Bachelorette"
            print("Navigating to page...")
            await page.goto(url, wait_until="domcontentloaded")

            print("Waiting for initial content and potential popups...")
            await page.wait_for_timeout(7000)

            # --- Robust Popup Handling ---
            popup_selector = "div#popup"
            popup = await page.query_selector(popup_selector)
            popup_closed_successfully = False

            if popup:
                print("Popup (div#popup) found. Attempting to dismiss.")
                is_popup_visible = await popup.evaluate('(element) => {\n    const computedStyles = window.getComputedStyle(element);\n    const rect = element.getBoundingClientRect();\n    return computedStyles.display !== "none" &&\n           computedStyles.visibility !== "hidden" &&\n           computedStyles.opacity !== "0" &&\n           rect.width > 0 && rect.height > 0;\n}')
                if is_popup_visible:
                    print("Popup is visible. Trying common dismissal methods.")
                    try:
                        print("Attempting to press Escape key...")
                        await page.press('body', 'Escape')
                        await page.wait_for_timeout(2000)
                        is_popup_visible_after_escape = await page.evaluate(f'(selector) => {{\n    const element = document.querySelector(selector);\n    if (!element) return false;\n    const computedStyles = window.getComputedStyle(element);\n    const rect = element.getBoundingClientRect();\n    return computedStyles.display !== "none" &&\n           computedStyles.visibility !== "hidden" &&\n           computedStyles.opacity !== "0" &&\n           rect.width > 0 && rect.height > 0;\n}}', popup_selector)
                        if not is_popup_visible_after_escape:
                            print("Popup dismissed by Escape key.")
                            popup_closed_successfully = True
                        else:
                            print("Popup still visible after pressing Escape. Trying to click outside.")
                            try:
                                await page.mouse.click(10, 10)
                                await page.wait_for_timeout(2000)
                                is_popup_visible_after_click = await page.evaluate(f'(selector) => {{\n    const element = document.querySelector(selector);\n    if (!element) return false;\n    const computedStyles = window.getComputedStyle(element);\n    const rect = element.getBoundingClientRect();\n    return computedStyles.display !== "none" &&\n           computedStyles.visibility !== "hidden" &&\n           computedStyles.opacity !== "0" &&\n           rect.width > 0 && rect.height > 0;\n}}', popup_selector)
                                if not is_popup_visible_after_click:
                                    print("Popup dismissed by clicking outside.")
                                    popup_closed_successfully = True
                                else:
                                    print("Popup still visible after clicking outside.")
                            except Exception as e:
                                print(f"Error clicking outside popup: {e}")
                    except Exception as e:
                        print(f"Error attempting to dismiss popup: {e}")
                    if not popup_closed_successfully:
                        print("Warning: Could not confirm popup dismissal using common methods.")
                else:
                    print("Popup (div#popup) found but is not visible.")
            else:
                print("Popup (div#popup) element not found.")

            print("Waiting for product cards...")
            await page.wait_for_selector(".product-card", state='attached', timeout=60000)
            await page.wait_for_timeout(2000)

            print("Collecting summary stats...")
            total_results = 0
            total_pages = 0
            results_summary = await page.query_selector(".results-summary")
            if results_summary:
                try:
                    total_results_text = await results_summary.inner_text()
                    parts = total_results_text.split(" ")
                    if len(parts) > 1 and parts[0].isdigit():
                        total_results = int(parts[0])
                    else:
                        print(f"Could not parse total results from: {total_results_text}")
                except Exception as e:
                    print(f"Error parsing total results: {e}")
            else:
                print("Warning: .results-summary not found.")

            paginator_last_button = await page.query_selector(".pagination .page-number:last-child")
            if paginator_last_button:
                try:
                    total_pages_text = await paginator_last_button.inner_text()
                    if total_pages_text.isdigit():
                        total_pages = int(total_pages_text)
                    else:
                        page_numbers = await page.query_selector_all(".pagination .page-number")
                        total_pages = len(page_numbers)
                except Exception as e:
                    print(f"Error parsing total pages: {e}")
            else:
                print("Warning: .pagination .page-number not found. Cannot reliably determine total pages this way.")

            print(f"Total results: {total_results}, Estimated total pages: {total_pages}")

            product_links = set()
            if total_pages > 0:
                print(f"Collecting product links across {total_pages} pages...")
                for page_num in range(1, total_pages + 1):
                    current_page_url = f"{url}&page={page_num}"
                    print(f"Going to page {page_num}: {current_page_url}")
                    await page.goto(current_page_url, wait_until="domcontentloaded")
                    await page.wait_for_selector(".product-card", state='attached', timeout=30000)
                    await page.wait_for_timeout(1000)
                    product_elements = await page.query_selector_all(".product-card")
                    print(f"Found {len(product_elements)} product cards on page {page_num}.")
                    for product in product_elements:
                        try:
                            product_link_el = await product.query_selector("a")
                            if product_link_el:
                                product_url = await product_link_el.get_attribute("href")
                                if product_url:
                                    if not product_url.startswith("http"):
                                        product_url = page.url.split('/pages/')[0] + product_url
                                    product_links.add(product_url)
                                else:
                                    print("Warning: Product link href attribute is empty.")
                            else:
                                print("Warning: Product link (a tag) not found within a product card.")
                        except Exception as e:
                            print(f"Error extracting product URL from a card: {e}")
            else:
                print("Could not determine total pages reliably. Scraping only the first page.")
                await page.goto(url, wait_until="domcontentloaded")
                await page.wait_for_selector(".product-card", state='attached', timeout=60000)
                await page.wait_for_timeout(2000)
                product_elements = await page.query_selector_all(".product-card")
                print(f"Found {len(product_elements)} product cards on the first page.")
                for product in product_elements:
                    try:
                        product_link_el = await product.query_selector("a")
                        if product_link_el:
                            product_url = await product_link_el.get_attribute("href")
                            if product_url:
                                if not product_url.startswith("http"):
                                    product_url = page.url.split('/pages/')[0] + product_url
                                product_links.add(product_url)
                            else:
                                print("Warning: Product link href attribute is empty.")
                        else:
                            print("Warning: Product link (a tag) not found within a product card.")
                    except Exception as e:
                        print(f"Error extracting product URL from a card: {e}")

            print(f"Total unique product links collected: {len(product_links)}")

            product_details = []
            print("Extracting details for each product...")
            for i, product_url in enumerate(list(product_links)):
                print(f"Scraping product {i+1}/{len(product_links)}: {product_url}")
                product_page = None
                try:
                    product_page = await context.new_page()
                    await product_page.goto(product_url, wait_until="domcontentloaded")
                    await product_page.wait_for_timeout(3000)

                    product_name_el = await product_page.query_selector("h1.product-title")
                    product_name = await product_name_el.text_content() if product_name_el else None
                    print(f" - Name: {product_name}")

                    price_regular_el = await product_page.query_selector(".price-regular")
                    price_regular = await price_regular_el.text_content() if price_regular_el else None
                    print(f" - Regular Price: {price_regular}")

                    price_sale_el = await product_page.query_selector(".price-sale")
                    price_sale = await price_sale_el.text_content() if price_sale_el else None
                    print(f" - Sale Price: {price_sale}")

                    sku_el = await product_page.query_selector(".sku")
                    sku = await sku_el.text_content() if sku_el else None
                    print(f" - SKU: {sku}")

                    availability_el = await product_page.query_selector(".availability")
                    availability = await availability_el.text_content() if availability_el else None
                    print(f" - Availability: {availability}")

                    description_el = await product_page.query_selector(".description")
                    description_html = await description_el.inner_html() if description_el else None

                    product_details.append({
                        "product_name": (product_name or "").strip(),
                        "product_url": product_url,
                        "price_regular": (price_regular or "").strip(),
                        "price_sale": (price_sale or "").strip(),
                        "variant_options": [],
                        "sku": (sku or "").strip(),
                        "availability": (availability or "").strip(),
                        "description_html": (description_html or "").strip(),
                        "image_urls": [],
                        "weight_or_size": "",
                        "collections": [],
                        "rating": "",
                        "review_count": ""
                    })

                    await product_page.close()
                except Exception as e:
                    print(f"Error extracting details for {product_url}: {e}")
                    if product_page:
                        await product_page.close()

            summary = {
                "total_results": total_results,
                "estimated_total_pages": total_pages,
                "links_collected": len(product_links),
                "details_extracted_for": len(product_details)
            }

            print("\nSummary:", json.dumps(summary, indent=4))

            with open("product_details.csv", "w", encoding="utf-8", newline="") as csv_file:
                fieldnames = [
                    "product_name", "product_url", "price_regular", "price_sale",
                    "variant_options", "sku", "availability", "description_html",
                    "image_urls", "weight_or_size", "collections", "rating", "review_count"
                ]
                writer = csv.DictWriter(csv_file, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(product_details)

            print("--End of Data--")
            await browser.close()
    except Exception as error:
        print("An error occurred during scraping:", error)

if __name__ == "__main__":
    asyncio.run(scrape_page())